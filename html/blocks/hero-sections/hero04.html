<!DOCTYPE html>
<html lang="en" data-palette="earth" class="">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>



<body class="bg-bg overflow-hidden lg-overflow-y-auto overflow-y-auto">

    <div data-nav-overlay data-navbar-id="app-nav" aria-hidden="true"
        class="fixed invisible fx-open-visible inset-0 lg-hidden bg-gray-800/60 backdrop-blur-xl z40">
    </div>
    <header class="absolute inset-x-0 top-0 z50 py6">
        <div class="mx-a lg-max-w-7xl w-full px5 sm-px10 md-px12 lg-px5">
            <nav class="w-full flex justify-between gap-6 relative">
                <div class="min-w-max inline-flex relative">
                    <a href="/" class="relative flex items-center gap-3">
                        <svg aria-hidden="true" width="488" height="488" class="size-8" viewBox="0 0 488 488"
                            fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_13_164)">
                                <path
                                    d="M71.5342 71L416.602 416.068V416.068C321.314 511.356 166.822 511.356 71.5342 416.068V416.068C-23.7537 320.78 -23.7537 166.288 71.5342 71V71Z"
                                    fill="currentColor" class="text-primary"></path>
                                <path
                                    d="M483.659 249.5C483.659 274.629 436.788 344.5 411.659 344.5C386.53 344.5 392.659 274.629 392.659 249.5C392.659 224.371 413.03 204 438.159 204C463.288 204 483.659 224.371 483.659 249.5Z"
                                    fill="currentColor" class="text-primary"></path>
                                <circle cx="243.429" cy="243.759" r="110" transform="rotate(48.3973 243.429 243.759)"
                                    fill="black" stroke="white" stroke-width="80" class="fill-primary stroke-bg">
                                </circle>
                                <path
                                    d="M309 71C309 96.129 212.629 101 187.5 101C162.371 101 142 80.629 142 55.5C142 30.371 162.371 10 187.5 10C212.629 10 309 45.871 309 71Z"
                                    fill="currentColor" class="text-primary"></path>
                                <path
                                    d="M417 101.5C417 126.629 415.129 215.5 390 215.5C364.871 215.5 326 126.629 326 101.5C326 76.371 346.371 56 371.5 56C396.629 56 417 76.371 417 101.5Z"
                                    fill="currentColor" class="text-primary"></path>
                            </g>
                            <defs>
                                <clipPath id="clip0_13_164">
                                    <rect width="488" height="488" fill="currentColor" class="text-bg"></rect>
                                </clipPath>
                            </defs>
                        </svg>
                        <div class="inline-flex text-lg font-semibold text-fg-title">
                            Unlead
                        </div>
                    </a>
                </div>

                <div id="app-nav" data-app-navbar class="flex invisible op0 translate-y-10 lg-visible lg-op100 fx-open-translate-y-0 fx-open-visible fx-open-op100 lg-translate-y-0 duration-300 ease-linear overflow-hidden 
                    flex flex-col gap-y6 gap-x4 lg-flex-row wfull lg-wmax
                    lg-justify-between lg-items-center absolute lg-relative top-14 lg-top-0 bg-bg 
                    b b-border lg-wmax ui-card c-rd-xl cp-1 z50 lg-shadow-sm">
                    <ul
                        class="py1 lg-py0 gap1.5  flex flex-col lg-flex-row text-fg w-full lg-justify-center lg-items-center text-sm">
                        <li class="flex">
                            <a href="#"
                                class="flex wfull duration-300 font-medium ease-linear hover-text-primary text-fg-muted p2 hover-bg-bg-surface inner-radius">
                                Features
                            </a>
                        </li>
                        <li class="flex">
                            <a href="#"
                                class="flex wfull duration-300 font-medium ease-linear hover-text-primary text-fg-muted p2 hover-bg-bg-surface inner-radius">
                                Resources
                            </a>
                        </li>
                        <li class="flex">
                            <a href="#"
                                class="flex wfull duration-300 font-medium ease-linear hover-text-primary text-fg-muted p2 hover-bg-bg-surface inner-radius">
                                Teams
                            </a>
                        </li>
                        <li class="flex">
                            <a href="#"
                                class="flex wfull duration-300 font-medium ease-linear hover-text-primary text-fg-muted py2 px4 hover-bg-bg-surface inner-radius">
                                Guide
                            </a>
                        </li>
                        <li class="flex">
                            <a href="#"
                                class="flex wfull duration-300 font-medium ease-linear hover-text-primary text-fg-muted py2 px4 hover-bg-bg-surface inner-radius">
                                Faq
                            </a>
                        </li>
                    </ul>
                </div>


                <div class="min-w-max flex items-center gap-x-3 relative">
                    <a href="#" class="btn btn-sm btn-outline btn-outline-gray rd-xl">
                        Get Started
                    </a>
                    <button data-nav-trigger data-toggle-nav="app-nav" aria-label="Toggle navbar"
                        class="lg:hidden lg:invisible outline-none w-7 h-auto flex flex-col relative group">
                        <span id="line-1"
                            class="w6 h-0.5 ro-full bg-fg-muted transition-all duration-300 ease-linear group-aria-expanded-translate-y-1.5 group-aria-expanded-rotate-40"></span>
                        <span id="line-2"
                            class="w-6 origin-center  mt-1 h-0.5 rd-ful bg-fg-muted transition-all duration-300 ease-linear group-aria-expanded-op0 group-aria-scale-x-0"></span>
                        <span id="line-3"
                            class="w6 mt-1 h-0.5 rd-ful bg-fg-muted transition-all duration-300 ease-linear group-aria-expanded--translate-y-1.5 group-aria-expanded--rotate-40"></span>
                    </button>
                </div>
            </nav>
        </div>
    </header>


    <section class="pt10 relative">
        <div class="absolute bottom-0 left-0 right-0 top-0 
          ui-grid [--unify-ui-grid-width:24px] [--unify-ui-grid-height:24px]
          ui-striped-overlay-mask text-fg-muted/10 ">
        </div>
        <div class="wfull flex items-center relative">
            <div class="min-h-max px5 sm-px10 md-px12 lg-px5 relative mxa pt32 lg-max-w7xl wfull text-center">
                <a href="#" class="flex items-center gap-x-2 mx-auto w-max px2 pr1 py1 rd-xl ui-subtle ui-subtle-gray">
                    <span class="i-ph-sparkle text-sm"></span>
                    AI Mode avaible
                    <span class="p1.5 rd-lg bg-bg-muted">
                        <span class=" i-ph-arrow-right text-sm flex"></span>
                    </span>
                </a>
                <h1
                    class="mt5 text-transparent bg-clip-text bg-gradient-to-br from-fg-title to-fg/50 text-4xl/tight sm-text-5xl/tight md-text-6xl/tight mxa max-w3xl font-bold">
                    Manage your leads, easier than ever before
                </h1>
                <p class="mx-auto max-wmd text-fg-muted mt6">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt saepe atque enim quasi
                </p>
                <form action="#" class="text-fg-muted bg-bg-surface focus-within-bg-bg b b-border-light focus-within-b-primary wfull flex gap3 items-center
                            rd-xl ease-linear pr1 wfull mx-auto max-w-xs mt6.5">
                    <input type="email" name="" id="" placeholder="<EMAIL>"
                        class="wfull bg-transparent outline-none py2 px4">
                    <button aria-label="sign up"
                        after="absolute content-empty inset-x-0 aspect-square scale-0 op-70 origin-center duration-300 ease-linear top-0 left-0 bg-purple-7 hover:op-100 hover:scale-[2.5]"
                        class="min-wmax btn btn-sm btn-solid btn-solid-primary text-white rd-lg">
                        <span class="hidden sm:flex relative z5">
                            Subscribe
                        </span>
                        <span class="flex sm-hidden relative z5">
                            <span class="i-ph-paper-plane-tilt text-xl"></span>
                        </span>
                    </button>
                </form>

                <div class="flex items-center gap-1 gap-x-2 justify-center mxa mt5">
                    <div
                        class="flex items-center -space-x-1 *-size-7 *-object-cover *-rd-full *-ring-2.5 *-ring-border">
                        <img src="/images/podCast.webp" width="2250" alt="listener avatar" class="">
                        <img src="/images/podCast.webp" width="2250" alt="listener avatar" class="">
                        <img src="/images/podCast.webp" width="2250" alt="listener avatar" class="">
                    </div>
                    <div class="flex flex-col justify-start items-start -space-y-1">
                        <div class="">
                            <span class="text-xs text-orange6">
                                &starf; &starf; &starf; &starf; &starf;
                            </span>
                        </div>
                        <span class="text-sm text-fg-muted/90">
                            +400 Lovely users
                        </span>
                    </div>
                </div>

                <div
                    class="mxa max-w4xl cp-1 c-rd-xl ui-card aspect-auto overflow-hidden bg-gradient-to-t from-bg-subtle to-bg-muted mt14">
                    <img src="/images/dash-light.webp" width="1440" height="1024" alt="product image"
                        class="inner-radius wfull hauto dark-hidden">
                    <img src="/images/dash-dark.webp" width="1440" height="1024" alt="product image"
                        class="inner-radius wfull hauto hidden dark-flex">
                </div>
            </div>
        </div>
    </section>



    <script type="module" src="/main.js"></script>
    <script type="module">
        import { toggleNavbar } from "@flexilla/utilities"
        toggleNavbar({
            navbarElement: "[data-app-navbar]", onToggle: ({ isExpanded }) => {
                document.body.classList[!isExpanded ? "add" : "remove"]("overflow-y-hidden")
            }
        })
    </script>
</body>

</html>
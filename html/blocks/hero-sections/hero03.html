<!DOCTYPE html>
<html lang="en" data-palette="fire">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>



<body class="bg-bg overflow-hidden lg-overflow-y-auto overflow-y-auto">

    <div data-nav-overlay data-navbar-id="app-nav" aria-hidden="true"
        class="fixed invisible fx-open-visible inset-0 lg-hidden bg-gray-800/60 backdrop-blur-xl z40">
    </div>
    <header class="absolute inset-x-0 top-0 z50 py6">
        <div class="mx-a lg-max-w-7xl w-full px5 sm-px10 md-px12 lg-px5">
            <nav class="w-full flex justify-between gap-6 relative">
                <div class="min-w-max inline-flex relative">
                    <a href="/" class="relative flex items-center gap-3">
                        <div class="relative w-7 h-7 overflow-hidden flex rounded-xl">
                            <span class="absolute size-4 -top-1 -right-1 bg-secondary red-md rotate-45"></span>
                            <span class="absolute size-4 -bottom-1 -right-1 bg-secondary red-md rotate-45"></span>
                            <span class="absolute size-4 -bottom-1 -left-1 bg-primary red-md rotate-45"></span>
                            <span class="absolute size-2 rd-full bg-primary500 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"></span>
                        </div>
                        <div class="inline-flex text-lg font-semibold text-fg-title">
                            AgenceX
                        </div>
                    </a>
                </div>

                <div id="app-nav" data-app-navbar class="flex invisible op0 translate-y-10 lg-visible lg-op100 fx-open-translate-y-0 fx-open-visible fx-open-op100 lg-translate-y-0 duration-300 ease-linear overflow-hidden 
                    flex flex-col gap-y-6 gap-x-4 lg:flex-row w-full lg-wmax lg-flex-1
                    lg-justify-between lg-items-center absolute lg-relative top-14 lg-top-0 bg-bg 
                    lg-bg-transparent b-x b-border lg-b-x-0 rd-xl z50">
                    <ul
                        class="border-t b-border lg-b-t-0 p6 lg-p0 flex flex-col lg-flex-row gap-y-4 gap-x-3 text-fg w-full lg-justify-center lg-items-center">
                        <li>
                            <a href="#"
                                class="duration-300 font-medium ease-linear hover-text-primary text-fg-muted py3">
                                Case studies
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="duration-300 font-medium ease-linear hover-text-primary text-fg-muted py3">
                                Services
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="duration-300 font-medium ease-linear hover-text-primary text-fg-muted py3">
                                About us
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="duration-300 font-medium ease-linear hover-text-primary text-fg-muted py3">
                                Faq
                            </a>
                        </li>
                    </ul>
                </div>


                <div class="min-w-max flex items-center gap-x-3">
                    <a href="#" class="btn btn-sm btn-solid btn-solid-primary text-white rd-xl">
                        Get Started
                    </a>
                    <button data-nav-trigger data-toggle-nav="app-nav" aria-label="Toggle navbar"
                        class="lg:hidden lg:invisible outline-none w-7 h-auto flex flex-col relative group">
                        <span id="line-1"
                            class="w6 h-0.5 ro-full bg-fg transition-all duration-300 ease-linear group-aria-expanded-translate-y-1.5 group-aria-expanded-rotate-40"></span>
                        <span id="line-2"
                            class="w-6 origin-center  mt-1 h-0.5 rd-ful bg-fg transition-all duration-300 ease-linear group-aria-expanded-op0 group-aria-scale-x-0"></span>
                        <span id="line-3"
                            class="w6 mt-1 h-0.5 rd-ful bg-fg transition-all duration-300 ease-linear group-aria-expanded--translate-y-1.5 group-aria-expanded--rotate-40"></span>
                    </button>
                </div>
            </nav>
        </div>
    </header>

    <section class="relative pt32 lg-pt36">
        <div class="mx-a lg-max-w-7xl w-full px5 sm-px10 md-px12 lg-px5 flex flex-col lg:flex-row gap-16">
            <div class="absolute wfull lg-w-1/2 inset-y-0 lg-right-0 hidden lg:block">
                <span class="absolute right-4 bottom-12 size-24 rd-3xl bg-primary blur-xl op40"></span>
            </div>
            <span
                class="w-4/12 lg-w-2/12 aspect-square bg-gradient-to-tr from-primary to-accent absolute -top-5 lg-left-0 rd-full skew-y-12 blur-2xl op30 skew-x-12 rotate-90"></span>
            <div class="relative flex flex-col items-center text-center lg-text-left lg-py7 xl-py8 
                lg-items-start lg-max-w-none max-w-3xl mx-a lg-mx-0 lg-flex-1 lg-w-1/2">
                <span class="ui-size-sm rd-xl ui-outline ui-outline-gray ">
                    Special Social
                </span>
                <h1 class="text-3xl/tight sm-text-4xl/tight md-text-5xl/tight xl-text-6xl/tight
                font-bold text-fg-title mt-8">
                    Revolutionize <span
                        class="text-transparent bg-clip-text bg-gradient-to-br from-primary from-20% via-secondary via-30% to-accent">
                        Your Brand</span> with Powerful Marketing!
                </h1>
                <p class="mt8 text-fg max-w-md">
                    Boost your brand’s visibility, engage your audience, and drive real results with cutting-edge social
                    media marketing strategies. It’s time to stand out and grow like never before!
                </p>
                <div class="mt10 w-full flex max-w-md mx-a lg-mx-0 lg-max-w-none gap-3 items-center flex-wrap justify-center lg-justify-start">
                    <a href="#" class="justify-center btn btn-lg btn-solid btn-solid-primary text-white rd-xl">
                        Get In Touch
                    </a>

                    <a href="#" class="justify-center btn btn-lg btn-outline btn-outline-gray text-fg rd-xl">
                        See our work
                    </a>
                </div>
            </div>

            <div class="flex flex-1 lg-w-1/2 lg-h-auto relative lg-max-w-none lg-mx-0 mx-auto max-w-3xl">
                <img src="/images/image1.webp" alt="Hero image" width="2350" height="2359"
                    class="lg:absolute lg:w-full lg:h-full rounded-xl object-cover lg:max-h-none max-h-96">
                <div
                    class="absolute w66 left-1/2 -translate-x-1/2 -bottom-6 p-2 bg-bg/80 backdrop-blur-md b b-border-strong/50 rd-xl shadow-sm shadow-bg-bg-muted/40 dark-shadow-transparent flex items-center before-absolute before-content-empty before-inset-x-3 before-h-2 before-flex before-bg-bg/80 before-b before-b-border-strong/50 before-bt-none before-rd-b-xl before--bottom-2 before-shadow-sm before-shadow-bg-bg-muted/40 before-shadow-transparent">
                    <span aria-hidden="true"
                        class="size-12 flex items-center justify-center rd-lg ui-soft ui-soft-primary mr-4">
                        <span class="flex i-ph-person text-2xl"></span>
                    </span>
                    <div class="flex flex-col">
                        <span class="font-semibold text-xl text-fg-title">+20</span>
                        <span class="text-sm text-fg-muted">Satisfaction</span>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <script type="module" src="/main.js"></script>
    <script type="module">
        import { toggleNavbar } from "@flexilla/utilities"
        toggleNavbar({
            navbarElement: "[data-app-navbar]", onToggle: ({ isExpanded }) => {
                document.body.classList[!isExpanded ? "add" : "remove"]("overflow-y-hidden")
            }
        })
    </script>
</body>

</html>
@import url(./colors.css);


html {
    min-width: 100vw;
    max-width: 100vw;
    scroll-behavior: smooth;
}

html,
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


@keyframes fadeIn {
    from {
        opacity: 0;
        visibility: hidden;
    }

    to {
        opacity: 1;
        visibility: visible;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        visibility: visible;
    }

    to {
        opacity: 0;
        visibility: hidden;
    }
}



@keyframes fadeInScale {
    from {
        opacity: 0;
        visibility: hidden;
        transform: scale(.95);
        -webkit-transform: scale(.95);
        -moz-transform: scale(.95);
        -ms-transform: scale(.95);
        -o-transform: scale(.95);
    }

    to {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }
}

@keyframes fadeOutScale {
    from {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }

    to {
        opacity: 0;
        visibility: hidden;
        transform: scale(.95);
        -webkit-transform: scale(.95);
        -moz-transform: scale(.95);
        -ms-transform: scale(.95);
        -o-transform: scale(.95);
    }
}


@keyframes slide-down-animation-modal {
    from {
        transform: translateY(-60px);
        opacity: 0;
        -webkit-transform: translateY(-60px);
        -moz-transform: translateY(-60px);
        -ms-transform: translateY(-60px);
        -o-transform: translateY(-60px);
    }

    to {
        transform: translateY(0);
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
    }
}



@keyframes slide-up-animation-modal {
    from {
        transform: translateY(60px);
        opacity: 0;
        -webkit-transform: translateY(60px);
        -moz-transform: translateY(60px);
        -ms-transform: translateY(60px);
        -o-transform: translateY(60px);
    }

    to {
        transform: translateY(0);
        opacity: 1;
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
    }
}
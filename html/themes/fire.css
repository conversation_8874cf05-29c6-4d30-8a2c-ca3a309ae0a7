[data-palette=fire] {
  --c-primary-50: var(--c-thunderbird-50);
  --c-primary-100: var(--c-thunderbird-100);
  --c-primary-200: var(--c-thunderbird-200);
  --c-primary-300: var(--c-thunderbird-300);
  --c-primary-400: var(--c-thunderbird-400);
  --c-primary-500: var(--c-thunderbird-500);
  --c-primary-600: var(--c-thunderbird-600);
  --c-primary-700: var(--c-thunderbird-700);
  --c-primary-800: var(--c-thunderbird-800);
  --c-primary-900: var(--c-thunderbird-900);
  --c-primary-950: var(--c-thunderbird-950);

  --c-secondary-50: var(--c-air-orange-50);
  --c-secondary-100: var(--c-air-orange-100);
  --c-secondary-200: var(--c-air-orange-200);
  --c-secondary-300: var(--c-air-orange-300);
  --c-secondary-400: var(--c-air-orange-400);
  --c-secondary-500: var(--c-air-orange-500);
  --c-secondary-600: var(--c-air-orange-600);
  --c-secondary-700: var(--c-air-orange-700);
  --c-secondary-800: var(--c-air-orange-800);
  --c-secondary-900: var(--c-air-orange-900);
  --c-secondary-950: var(--c-air-orange-950);

  
  --c-gray-50: var(--c-stone-50);
  --c-gray-100: var(--c-stone-100);
  --c-gray-200: var(--c-stone-200);
  --c-gray-300: var(--c-stone-300);
  --c-gray-400: var(--c-stone-400);
  --c-gray-500: var(--c-stone-500);
  --c-gray-600: var(--c-stone-600);
  --c-gray-700: var(--c-stone-700);
  --c-gray-800: var(--c-stone-800);
  --c-gray-900: var(--c-stone-900);
  --c-gray-950: var(--c-stone-950);
}
